<component name="libraryTable">
  <library name="Gradle: androidx.arch.core:core-runtime:2.2.0@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/8f584d735ff9bfd7953105e9d7907cfc/transformed/core-runtime-2.2.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/8f584d735ff9bfd7953105e9d7907cfc/transformed/core-runtime-2.2.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/8f584d735ff9bfd7953105e9d7907cfc/transformed/core-runtime-2.2.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-runtime/2.2.0/48630ad88f438e5a603ae74cf4858203d1a595c5/core-runtime-2.2.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>