<component name="libraryTable">
  <library name="Gradle: androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar" external-system-id="GRADLE">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.coordinatorlayout/coordinatorlayout/1.2.0/7a6b816ee556c56cacfa677a6fc98a5fd0e97373/coordinatorlayout-1.2.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>