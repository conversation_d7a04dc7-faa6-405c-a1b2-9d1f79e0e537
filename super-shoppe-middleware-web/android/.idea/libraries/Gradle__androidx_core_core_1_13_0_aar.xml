<component name="libraryTable">
  <library name="Gradle: androidx.core:core:1.13.0@aar" external-system-id="GRADLE">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/0ecad6c7bd9f64a4bb77f3cb4787772c/transformed/core-1.13.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/0ecad6c7bd9f64a4bb77f3cb4787772c/transformed/core-1.13.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/0ecad6c7bd9f64a4bb77f3cb4787772c/transformed/core-1.13.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/0ecad6c7bd9f64a4bb77f3cb4787772c/transformed/core-1.13.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.core/core/1.13.0/9ae580505557c829bed91310494b3ccd0a386dfd/core-1.13.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>