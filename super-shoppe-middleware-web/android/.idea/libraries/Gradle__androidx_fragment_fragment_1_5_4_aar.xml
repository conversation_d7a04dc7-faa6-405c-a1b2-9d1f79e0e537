<component name="libraryTable">
  <library name="Gradle: androidx.fragment:fragment:1.5.4@aar" external-system-id="GRADLE">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/b4e217b64c604c44afe62d34b0265915/transformed/fragment-1.5.4/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/b4e217b64c604c44afe62d34b0265915/transformed/fragment-1.5.4/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/b4e217b64c604c44afe62d34b0265915/transformed/fragment-1.5.4/res" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/b4e217b64c604c44afe62d34b0265915/transformed/fragment-1.5.4/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.fragment/fragment/1.5.4/5404b0f8aeccb2fe882bee99ef8843711c01c156/fragment-1.5.4-sources.jar!/" />
    </SOURCES>
  </library>
</component>