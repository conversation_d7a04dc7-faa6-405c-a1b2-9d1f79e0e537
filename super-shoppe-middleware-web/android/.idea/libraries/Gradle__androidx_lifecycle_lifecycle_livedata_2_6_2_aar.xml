<component name="libraryTable">
  <library name="Gradle: androidx.lifecycle:lifecycle-livedata:2.6.2@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/d68bb0f6b9dc7d41a2b6dfc3b4acad60/transformed/lifecycle-livedata-2.6.2/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/d68bb0f6b9dc7d41a2b6dfc3b4acad60/transformed/lifecycle-livedata-2.6.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/d68bb0f6b9dc7d41a2b6dfc3b4acad60/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-livedata/2.6.2/7ee67be9804a0951093b89b1115f95facb1d823/lifecycle-livedata-2.6.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>