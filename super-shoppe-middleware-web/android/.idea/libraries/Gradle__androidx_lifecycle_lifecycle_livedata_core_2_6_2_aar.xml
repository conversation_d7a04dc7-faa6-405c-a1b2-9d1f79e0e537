<component name="libraryTable">
  <library name="Gradle: androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/7cb5e40cf228b6c33a81f0245b3675c9/transformed/lifecycle-livedata-core-2.6.2/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/7cb5e40cf228b6c33a81f0245b3675c9/transformed/lifecycle-livedata-core-2.6.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/7cb5e40cf228b6c33a81f0245b3675c9/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-livedata-core/2.6.2/3b4141d2e2e1ff6ddfd8f1990283d579d2e2f7a4/lifecycle-livedata-core-2.6.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>