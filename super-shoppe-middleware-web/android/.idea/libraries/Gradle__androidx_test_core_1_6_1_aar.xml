<component name="libraryTable">
  <library name="Gradle: androidx.test:core:1.6.1@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/0dfb94e8e9f7930d0d4cbce98a17f1aa/transformed/core-1.6.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/0dfb94e8e9f7930d0d4cbce98a17f1aa/transformed/core-1.6.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/0dfb94e8e9f7930d0d4cbce98a17f1aa/transformed/core-1.6.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.test/core/1.6.1/373d480f7ae91a32ac4ee1583709af21f319c951/core-1.6.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>