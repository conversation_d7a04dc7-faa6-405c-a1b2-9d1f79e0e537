<component name="libraryTable">
  <library name="Gradle: androidx.test.ext:junit:1.2.1@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/e9a821fdfd7726d216f9a93c4c7b7c07/transformed/junit-1.2.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/e9a821fdfd7726d216f9a93c4c7b7c07/transformed/junit-1.2.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.test.ext/junit/1.2.1/f170b63147430a6b8074a09070d1759edd0237b6/junit-1.2.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>