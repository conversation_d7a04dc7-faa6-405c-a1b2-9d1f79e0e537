<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-datatransport:18.2.0@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/d59858b3709795c3a4e2c9928bb49778/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-datatransport/18.2.0/1d85f96dd0423ecc564552cda8b1c556eb23716f/firebase-datatransport-18.2.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>