<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-measurement-connector:19.0.0@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/3cd5cde5baf76ffd76ccca9ac0ba6533/transformed/firebase-measurement-connector-19.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/3cd5cde5baf76ffd76ccca9ac0ba6533/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-measurement-connector/19.0.0/c0d58b8efa926b2c607984c54fb3d274490badd1/firebase-measurement-connector-19.0.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>