<component name="libraryTable">
  <library name="Gradle: io.ionic.libs:iongeolocation-android:1.0.0@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/8.11.1/transforms/cf5c25e299eb52ddcbcc73083ca78071/transformed/iongeolocation-android-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/8.11.1/transforms/cf5c25e299eb52ddcbcc73083ca78071/transformed/iongeolocation-android-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/io.ionic.libs/iongeolocation-android/1.0.0/ce65dffe5b854eda4a69e6daaa025ad3e1dd6e8/iongeolocation-android-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>