<component name="libraryTable">
  <library name="Gradle: org.jetbrains.kotlin:kotlin-stdlib:1.9.25" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.jetbrains.kotlin" artifactId="kotlin-stdlib" version="1.9.25" baseVersion="1.9.25" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.25/f700a2f2b8f0d6d0fde48f56d894dc722fb029d7/kotlin-stdlib-1.9.25.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.25/2ad14aed781c4a73ed4dbb421966d408a0a06686/kotlin-stdlib-1.9.25-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.25/e112f0203eecd8c07fb68396686050bc604ccceb/kotlin-stdlib-1.9.25-sources.jar!/" />
    </SOURCES>
  </library>
</component>