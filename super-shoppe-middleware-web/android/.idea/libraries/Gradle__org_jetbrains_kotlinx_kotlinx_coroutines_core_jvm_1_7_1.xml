<component name="libraryTable">
  <library name="Gradle: org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.jetbrains.kotlinx" artifactId="kotlinx-coroutines-core-jvm" version="1.7.1" baseVersion="1.7.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.7.1/63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4/kotlinx-coroutines-core-jvm-1.7.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.7.1/e653e51cc736c8e47cd9a62080f4adfce8330614/kotlinx-coroutines-core-jvm-1.7.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.7.1/9ba46d8eb284ab4db835daebb561d8e5e918467e/kotlinx-coroutines-core-jvm-1.7.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>