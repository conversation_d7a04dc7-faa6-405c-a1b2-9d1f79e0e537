<component name="libraryTable">
  <library name="Gradle: org.mockito:mockito-core:5.14.1" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.mockito" artifactId="mockito-core" version="5.14.1" baseVersion="5.14.1" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/5.14.1/a89b0ce9ee5d92646522caeb27fb92c02a0b4c55/mockito-core-5.14.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/5.14.1/5f4bd7e95a39e5eee4a7d8bd0f16221dfb95df6f/mockito-core-5.14.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.mockito/mockito-core/5.14.1/b6340f99461a2b1ee196631ceea54c7db73f434d/mockito-core-5.14.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>