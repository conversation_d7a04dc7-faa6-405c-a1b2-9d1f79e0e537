<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/android.iml" filepath="$PROJECT_DIR$/.idea/modules/android.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/android.app.iml" filepath="$PROJECT_DIR$/.idea/modules/app/android.app.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/android.app.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/app/android.app.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/android.app.main.iml" filepath="$PROJECT_DIR$/.idea/modules/app/android.app.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/app/android.app.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/app/android.app.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1851588910/android.capacitor-android.iml" filepath="$PROJECT_DIR$/.idea/modules/1851588910/android.capacitor-android.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1851588910/android.capacitor-android.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1851588910/android.capacitor-android.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1851588910/android.capacitor-android.main.iml" filepath="$PROJECT_DIR$/.idea/modules/1851588910/android.capacitor-android.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1851588910/android.capacitor-android.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1851588910/android.capacitor-android.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/391706765/android.capacitor-app.iml" filepath="$PROJECT_DIR$/.idea/modules/391706765/android.capacitor-app.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/391706765/android.capacitor-app.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/391706765/android.capacitor-app.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/391706765/android.capacitor-app.main.iml" filepath="$PROJECT_DIR$/.idea/modules/391706765/android.capacitor-app.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/391706765/android.capacitor-app.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/391706765/android.capacitor-app.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1537225580/android.capacitor-browser.iml" filepath="$PROJECT_DIR$/.idea/modules/-1537225580/android.capacitor-browser.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1537225580/android.capacitor-browser.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1537225580/android.capacitor-browser.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1537225580/android.capacitor-browser.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-1537225580/android.capacitor-browser.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1537225580/android.capacitor-browser.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1537225580/android.capacitor-browser.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1597556471/android.capacitor-community-privacy-screen.iml" filepath="$PROJECT_DIR$/.idea/modules/-1597556471/android.capacitor-community-privacy-screen.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1597556471/android.capacitor-community-privacy-screen.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1597556471/android.capacitor-community-privacy-screen.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1597556471/android.capacitor-community-privacy-screen.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-1597556471/android.capacitor-community-privacy-screen.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1597556471/android.capacitor-community-privacy-screen.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1597556471/android.capacitor-community-privacy-screen.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1586525786/android.capacitor-community-screen-brightness.iml" filepath="$PROJECT_DIR$/.idea/modules/1586525786/android.capacitor-community-screen-brightness.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1586525786/android.capacitor-community-screen-brightness.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1586525786/android.capacitor-community-screen-brightness.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1586525786/android.capacitor-community-screen-brightness.main.iml" filepath="$PROJECT_DIR$/.idea/modules/1586525786/android.capacitor-community-screen-brightness.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1586525786/android.capacitor-community-screen-brightness.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1586525786/android.capacitor-community-screen-brightness.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/capacitor-cordova-android-plugins/android.capacitor-cordova-android-plugins.iml" filepath="$PROJECT_DIR$/.idea/modules/capacitor-cordova-android-plugins/android.capacitor-cordova-android-plugins.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/capacitor-cordova-android-plugins/android.capacitor-cordova-android-plugins.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/capacitor-cordova-android-plugins/android.capacitor-cordova-android-plugins.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/capacitor-cordova-android-plugins/android.capacitor-cordova-android-plugins.main.iml" filepath="$PROJECT_DIR$/.idea/modules/capacitor-cordova-android-plugins/android.capacitor-cordova-android-plugins.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/capacitor-cordova-android-plugins/android.capacitor-cordova-android-plugins.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/capacitor-cordova-android-plugins/android.capacitor-cordova-android-plugins.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-169470934/android.capacitor-device.iml" filepath="$PROJECT_DIR$/.idea/modules/-169470934/android.capacitor-device.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-169470934/android.capacitor-device.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-169470934/android.capacitor-device.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-169470934/android.capacitor-device.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-169470934/android.capacitor-device.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-169470934/android.capacitor-device.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-169470934/android.capacitor-device.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1656638322/android.capacitor-geolocation.iml" filepath="$PROJECT_DIR$/.idea/modules/1656638322/android.capacitor-geolocation.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1656638322/android.capacitor-geolocation.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1656638322/android.capacitor-geolocation.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1656638322/android.capacitor-geolocation.main.iml" filepath="$PROJECT_DIR$/.idea/modules/1656638322/android.capacitor-geolocation.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1656638322/android.capacitor-geolocation.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1656638322/android.capacitor-geolocation.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1266731620/android.capacitor-preferences.iml" filepath="$PROJECT_DIR$/.idea/modules/1266731620/android.capacitor-preferences.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1266731620/android.capacitor-preferences.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1266731620/android.capacitor-preferences.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1266731620/android.capacitor-preferences.main.iml" filepath="$PROJECT_DIR$/.idea/modules/1266731620/android.capacitor-preferences.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1266731620/android.capacitor-preferences.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1266731620/android.capacitor-preferences.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1429170103/android.capacitor-push-notifications.iml" filepath="$PROJECT_DIR$/.idea/modules/-1429170103/android.capacitor-push-notifications.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1429170103/android.capacitor-push-notifications.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1429170103/android.capacitor-push-notifications.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1429170103/android.capacitor-push-notifications.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-1429170103/android.capacitor-push-notifications.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1429170103/android.capacitor-push-notifications.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/-1429170103/android.capacitor-push-notifications.unitTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1169021843/android.capacitor-toast.iml" filepath="$PROJECT_DIR$/.idea/modules/1169021843/android.capacitor-toast.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1169021843/android.capacitor-toast.androidTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1169021843/android.capacitor-toast.androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1169021843/android.capacitor-toast.main.iml" filepath="$PROJECT_DIR$/.idea/modules/1169021843/android.capacitor-toast.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/1169021843/android.capacitor-toast.unitTest.iml" filepath="$PROJECT_DIR$/.idea/modules/1169021843/android.capacitor-toast.unitTest.iml" />
    </modules>
  </component>
</project>