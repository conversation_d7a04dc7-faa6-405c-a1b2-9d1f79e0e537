<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":capacitor-community-privacy-screen:main" external.linked.project.path="$MODULE_DIR$/../../../../node_modules/@capacitor-community/privacy-screen/android" external.root.project.path="$MODULE_DIR$/../../.." external.system.id="GRADLE" external.system.module.type="sourceSet" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet external-system-id="GRADLE" type="android" name="Android">
      <configuration>
        <option name="SELECTED_BUILD_VARIANT" value="debug" />
        <option name="ALLOW_USER_CONFIGURATION" value="false" />
        <option name="MANIFEST_FILE_RELATIVE_PATH" value="/src/main/AndroidManifest.xml" />
        <option name="RES_FOLDER_RELATIVE_PATH" value="/src/main/res" />
        <option name="ASSETS_FOLDER_RELATIVE_PATH" value="/src/main/assets" />
        <option name="PROJECT_TYPE" value="1" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_21">
    <output url="file://$MODULE_DIR$/../../../../node_modules/@capacitor-community/privacy-screen/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes" />
    <exclude-output />
    <content url="file://$MODULE_DIR$/../../../../node_modules/@capacitor-community/privacy-screen/android/build/generated/ap_generated_sources/debug/out">
      <sourceFolder url="file://$MODULE_DIR$/../../../../node_modules/@capacitor-community/privacy-screen/android/build/generated/ap_generated_sources/debug/out" isTestSource="false" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../../../node_modules/@capacitor-community/privacy-screen/android/build/generated/res/resValues/debug">
      <sourceFolder url="file://$MODULE_DIR$/../../../../node_modules/@capacitor-community/privacy-screen/android/build/generated/res/resValues/debug" type="java-resource" />
    </content>
    <content url="file://$MODULE_DIR$/../../../../node_modules/@capacitor-community/privacy-screen/android/src/debug" />
    <content url="file://$MODULE_DIR$/../../../../node_modules/@capacitor-community/privacy-screen/android/src/main">
      <sourceFolder url="file://$MODULE_DIR$/../../../../node_modules/@capacitor-community/privacy-screen/android/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/../../../../node_modules/@capacitor-community/privacy-screen/android/src/main/res" type="java-resource" />
    </content>
    <orderEntry type="jdk" jdkName="Android API 35, extension level 13 Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module" module-name="android.capacitor-android.main" />
    <orderEntry type="library" name="Gradle: androidx.appcompat:appcompat:1.7.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.activity:activity:1.9.2@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.core:core:1.15.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.annotation:annotation-experimental:1.4.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-runtime:2.6.2@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.versionedparcelable:versionedparcelable:1.1.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.core:core-ktx:1.15.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.savedstate:savedstate:1.2.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.appcompat:appcompat-resources:1.7.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.vectordrawable:vectordrawable:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.vectordrawable:vectordrawable-animated:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.interpolator:interpolator:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.cursoradapter:cursoradapter:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.drawerlayout:drawerlayout:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.customview:customview:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.fragment:fragment:1.8.4@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.loader:loader:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-livedata:2.6.2@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.arch.core:core-runtime:2.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.viewpager:viewpager:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.annotation:annotation-jvm:1.8.1" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib:1.9.25" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains:annotations:13.0" level="project" />
    <orderEntry type="library" name="Gradle: androidx.arch.core:core-common:2.2.0" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-common:2.6.2" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.25" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25" level="project" />
    <orderEntry type="library" name="Gradle: androidx.collection:collection-jvm:1.4.2" level="project" />
  </component>
</module>