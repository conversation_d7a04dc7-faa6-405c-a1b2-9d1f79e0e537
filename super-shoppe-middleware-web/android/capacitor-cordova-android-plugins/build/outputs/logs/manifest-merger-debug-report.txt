-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml:2:1-8:12
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml:2:1-8:12
	package
		INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml
	xmlns:amazon
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml:3:1-57
	xmlns:android
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml:2:11-69
application
ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml:4:1-6:15
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml:4:15-50
uses-sdk
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/bitbucket/ecommerce/super-shoppe-middleware-web/android/capacitor-cordova-android-plugins/src/main/AndroidManifest.xml
