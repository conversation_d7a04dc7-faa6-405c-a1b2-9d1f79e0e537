import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'my.inventorymanagement.app',
  appName: 'Inventory Management',
  webDir: 'out',
  server: {
    url: 'http://192.168.0.120:3000',
    cleartext: true,
  },
  ios: {
    scheme: 'Inventory Management',
  },
  plugins: {
    CapacitorCookies: {
      enabled: true,
    },
    PrivacyScreen: {
      enable: false,
    },
    PushNotifications: {
      presentationOptions: ['badge', 'sound', 'alert'],
    },
  },
};

export default config;
