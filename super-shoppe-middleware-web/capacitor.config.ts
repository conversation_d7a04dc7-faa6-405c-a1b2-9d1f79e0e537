import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'my.supershoppe.app',
  appName: 'Super Shoppe',
  webDir: 'out',
  // server: {
  //   url: 'http://192.168.0.120:3000',
  //   cleartext: true,
  // },
  ios: {
    scheme: 'Super Shoppe',
  },
  plugins: {
    CapacitorCookies: {
      enabled: true,
    },
    PrivacyScreen: {
      enable: false,
    },
    PushNotifications: {
      presentationOptions: ['badge', 'sound', 'alert'],
    },
  },
};

export default config;
