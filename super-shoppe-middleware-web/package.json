{"name": "inventory-management-app", "version": "1.0.3", "private": true, "scripts": {"clean": "rimraf \"{.next,dist,build,out,node_modules}\"", "dev": "next dev", "build": "next build && next export", "start": "next start", "lint": "next lint", "static": "yarn postinstall && next build && next export", "build-android": "yarn postinstall && next build && next export && npx cap sync android", "build-ios": "yarn postinstall && next build && next export && npx cap sync ios", "format": "prettier --write .", "check-types": "tsc --pretty --noEmit", "check-format": "prettier --check .", "test-all": "yarn check-format && yarn lint && yarn check-types && yarn build", "prepare": "husky install", "postinstall": "node ./postinstall/post-script.js"}, "dependencies": {"@capacitor-community/privacy-screen": "^6.0.0", "@capacitor-community/screen-brightness": "^7.0.0", "@capacitor/android": "^7.0.0", "@capacitor/app": "^7.0.0", "@capacitor/browser": "^7.0.0", "@capacitor/core": "^7.0.0", "@capacitor/device": "^7.0.0", "@capacitor/geolocation": "^7.0.0", "@capacitor/ios": "^7.0.0", "@capacitor/preferences": "^7.0.0", "@capacitor/push-notifications": "^7.0.0", "@capacitor/toast": "^7.0.0", "@headlessui/react": "^1.4.1", "@hookform/resolvers": "^2.6.0", "@react-pdf/renderer": "2.0.21", "@tailwindcss/forms": "^0.3.4", "@types/cleave.js": "^1.4.4", "@types/react-datepicker": "^4.4.2", "@vis.gl/react-google-maps": "^1.1.0", "axios": "^0.24.0", "camelcase-keys": "^7.0.0", "capacitor-plugin-app-tracking-transparency": "^2.0.5", "classnames": "^2.3.1", "cleave.js": "^1.6.0", "cordova-plugin-inappbrowser": "^5.0.0", "dayjs": "^1.10.7", "framer-motion": "^6.0.0", "graphql": "^16.6.0", "graphql-request": "^5.0.0", "iconv-lite": "^0.6.3", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "^12.3.1", "next-compose-plugins": "^2.2.1", "next-export-i18n": "^2.1.0", "next-i18next": "^8.9.0", "next-pwa": "^5.4.0", "next-seo": "^4.28.1", "overlayscrollbars": "^1.13.1", "overlayscrollbars-react": "^0.2.3", "rc-drawer": "^4.4.2", "rc-pagination": "^3.1.9", "rc-table": "^7.19.0", "react": "17.0.2", "react-barcode": "^1.4.6", "react-content-loader": "^6.0.3", "react-copy-to-clipboard": "^5.0.4", "react-countdown": "^2.3.2", "react-datepicker": "^4.8.0", "react-dom": "17.0.2", "react-hook-form": "^7.31.2", "react-icons": "^4.3.1", "react-laag": "^2.0.3", "react-lazy-load-image-component": "^1.5.6", "react-player": "^2.9.0", "react-qr-code": "^2.0.8", "react-query": "^3.28.0", "react-scroll": "^1.8.4", "react-select": "5.1.0", "react-share": "^4.4.0", "react-star-rating-component": "^1.4.1", "react-toastify": "^8.0.3", "react-use": "^17.3.1", "react-waypoint": "^10.1.0", "sharp": "^0.29.2", "swiper": "^6.7.1", "yup": "^0.32.9"}, "devDependencies": {"@capacitor/cli": "^7.0.0", "@types/body-parser": "^1.19.2", "@types/js-cookie": "^3.0.0", "@types/leaflet": "^1.7.5", "@types/lodash": "^4.14.176", "@types/node": "^16.11.5", "@types/overlayscrollbars": "^1.12.1", "@types/react": "^17.0.32", "@types/react-copy-to-clipboard": "^5.0.2", "@types/react-lazy-load-image-component": "^1.5.3", "@types/react-scroll": "^1.8.3", "@types/react-select": "^5.0.1", "@types/react-star-rating-component": "^1.4.1", "autoprefixer": "10.4.5", "husky": "^7.0.4", "postcss": "^8.3.11", "prettier": "^2.4.1", "rimraf": "^3.0.2", "tailwindcss": "^2.2.17", "tailwindcss-rtl": "^0.7.3", "typescript": "^4.4.4"}}