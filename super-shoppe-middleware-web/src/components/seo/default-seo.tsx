import { DefaultSeo as NextDefaultSeo, DefaultSeoProps } from 'next-seo';
import { siteSettings } from '@settings/site-settings';
import { useSettings } from '@contexts/settings.context';

export const DefaultSeo = ({
  title,
  titleTemplate,
  description,
}: DefaultSeoProps) => {
  const settings = useSettings();
  return (
    <NextDefaultSeo
      title={title ?? (settings ? settings.siteSubtitle : siteSettings.name)}
      titleTemplate={
        titleTemplate ??
        `${settings ? settings.siteTitle : siteSettings.name} | %s`
      }
      defaultTitle={settings ? settings.siteTitle : siteSettings.name}
      description={settings ? settings.siteSubtitle : siteSettings.description}
      canonical={
        settings && settings.seo && settings.seo.canonicalUrl
          ? settings.seo.canonicalUrl
          : 'https://inventorymanagement.com/'
      }
      openGraph={{
        type: 'website',
        locale: 'en_IE',
        site_name: settings ? settings.siteTitle : siteSettings.name,
        title:
          settings && settings.seo && settings.seo.ogTitle
            ? settings.seo.ogTitle
            : title,
        description:
          settings && settings.seo && settings.seo.ogDescription
            ? settings.seo.ogDescription
            : description,
        images: [
          ...(settings && settings.seo && settings.seo.ogImage
            ? [
                {
                  url: settings.seo.ogImage.original,
                  alt:
                    settings && settings.seo && settings.seo.ogTitle
                      ? settings.seo.ogTitle
                      : title,
                },
              ]
            : []),
        ],
      }}
      twitter={{
        handle:
          settings && settings.seo && settings.seo.twitterHandle
            ? settings.seo.twitterHandle
            : '@handle',
        site: settings ? settings.siteTitle : siteSettings.name,
        cardType:
          settings && settings.seo && settings.seo.twitterCardType
            ? settings.seo.twitterCardType
            : 'summary_large_image',
      }}
      additionalMetaTags={[
        {
          name: 'metaTitle',
          content:
            settings && settings.seo && settings.seo.metaTitle
              ? settings.seo.metaTitle
              : title,
        },
        {
          name: 'metaDescription',
          content:
            settings && settings.seo && settings.seo.metaDescription
              ? settings.seo.metaDescription
              : description,
        },
        {
          name: 'metaTags',
          content:
            settings && settings.seo && settings.seo.metaTags
              ? settings.seo.metaTags
              : '',
        },
        {
          name: 'viewport',
          content:
            'width=device-width, initial-scale=1 maximum-scale=1, viewport-fit=cover',
        },
        {
          name: 'apple-mobile-web-app-capable',
          content: 'yes',
        },
        {
          name: 'theme-color',
          content: '#ffffff',
        },
      ]}
      additionalLinkTags={[
        {
          rel: 'apple-touch-icon',
          href: 'icons/apple-icon-180.png',
        },
        {
          rel: 'manifest',
          href: '/manifest.json',
        },
      ]}
    />
  );
};
